// === NEW CLASS-BASED TRADING BOT ===
import { TradingBot } from "./src/TradingBot.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// === CLASS-BASED BOT INITIALIZATION ===
const tradingBot = new TradingBot({
  symbol: process.env.SYMBOL || "ETHUSDT",
  interval: process.env.INTERVAL || "1h",
  mode: process.env.BOT_MODE || "ai_vision", // ai_vision, rule_based, legacy
  telegramToken: process.env.TELEGRAM_BOT_TOKEN,
  chatId: process.env.TELEGRAM_GROUP_ID,
  enableFallback: true,
  runInterval: 60 * 60 * 1000, // 1 hour
  
  // Handler configurations
  gptConfig: {
    apiKey: process.env.OPENAI_API_KEY,
    visionModel: "gpt-4o",
    textModel: "gpt-4o-mini"
  },
  binanceConfig: {
    apiKey: process.env.BINANCE_API_KEY,
    apiSecret: process.env.BINANCE_API_SECRET,
    isTestnet: process.env.BINANCE_TESTNET === "true"
  }
});

// === MAIN EXECUTION ===
async function main() {
  try {
    console.log("🚀 Starting Trading Bot with Class-Based Architecture");
    
    // Test all connections
    const connectionTests = await tradingBot.testConnections();
    console.log("🔍 Connection test results:", connectionTests);
    
    if (!connectionTests.overall) {
      console.warn("⚠️ Some connections failed, but continuing with available services");
    }
    
    // Start the bot scheduler
    tradingBot.startScheduler();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\n🛑 Received SIGINT, shutting down gracefully...');
      await tradingBot.shutdown();
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
      await tradingBot.shutdown();
      process.exit(0);
    });
    
  } catch (error) {
    console.error("❌ Failed to start trading bot:", error);
    process.exit(1);
  }
}

// === START THE BOT ===
main().catch((error) => {
  console.error("❌ Failed to start trading bot:", error);
  process.exit(1);
});

// === EXPORTS FOR TESTING AND EXTERNAL USE ===
export {
  TradingBot,
  tradingBot
};
