import axios from "axios";
import crypto from "crypto";

/**
 * Binance<PERSON><PERSON><PERSON> - Manages all interactions with Binance API
 * Responsibilities:
 * - Market data retrieval (klines, ticker, orderbook)
 * - Account information (balance, positions)
 * - Order management (place, cancel, modify orders)
 * - WebSocket connections for real-time data
 * - Rate limiting and error handling
 */
export class BinanceHandler {
  constructor(config = {}) {
    this.config = {
      // API endpoints
      baseUrl: config.baseUrl || "https://api.binance.com",
      testnetUrl: config.testnetUrl || "https://testnet.binance.vision",

      // API credentials (optional for public endpoints)
      apiKey: config.apiKey || process.env.BINANCE_API_KEY,
      apiSecret: config.apiSecret || process.env.BINANCE_API_SECRET,

      // Trading settings
      isTestnet: config.isTestnet || false,
      defaultSymbol: config.defaultSymbol || "ETHUSDT",

      // Request settings
      timeout: config.timeout || 10000,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,

      ...config,
    };

    // Set base URL based on testnet setting
    this.baseUrl = this.config.isTestnet
      ? this.config.testnetUrl
      : this.config.baseUrl;

    // Rate limiting
    this.requestCount = 0;
    this.lastRequestTime = 0;
    this.rateLimitWindow = 60000; // 1 minute
    this.maxRequestsPerMinute = 1200; // Binance limit
  }

  /**
   * Create authenticated request headers
   * @param {Object} params - Request parameters
   * @returns {Object} Headers with signature
   */
  createAuthHeaders(params = {}) {
    if (!this.config.apiKey || !this.config.apiSecret) {
      throw new Error("API key and secret required for authenticated requests");
    }

    const timestamp = Date.now();
    const queryString = new URLSearchParams({
      ...params,
      timestamp,
    }).toString();

    const signature = crypto
      .createHmac("sha256", this.config.apiSecret)
      .update(queryString)
      .digest("hex");

    return {
      "X-MBX-APIKEY": this.config.apiKey,
      "Content-Type": "application/json",
    };
  }

  /**
   * Check rate limits before making requests
   */
  checkRateLimit() {
    const now = Date.now();

    // Reset counter if window has passed
    if (now - this.lastRequestTime > this.rateLimitWindow) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }

    if (this.requestCount >= this.maxRequestsPerMinute) {
      const waitTime = this.rateLimitWindow - (now - this.lastRequestTime);
      throw new Error(
        `Rate limit exceeded. Wait ${Math.ceil(waitTime / 1000)} seconds`
      );
    }

    this.requestCount++;
  }

  /**
   * Make HTTP request with retry logic
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(endpoint, options = {}) {
    const {
      method = "GET",
      params = {},
      authenticated = false,
      retryCount = 0,
    } = options;

    try {
      // Check rate limits
      this.checkRateLimit();

      const url = `${this.baseUrl}${endpoint}`;
      const config = {
        method,
        url,
        timeout: this.config.timeout,
        params: method === "GET" ? params : undefined,
        data: method !== "GET" ? params : undefined,
      };

      // Add authentication if required
      if (authenticated) {
        config.headers = this.createAuthHeaders(params);

        // Add signature to params
        const timestamp = Date.now();
        const allParams = { ...params, timestamp };
        const queryString = new URLSearchParams(allParams).toString();
        const signature = crypto
          .createHmac("sha256", this.config.apiSecret)
          .update(queryString)
          .digest("hex");

        allParams.signature = signature;

        if (method === "GET") {
          config.params = allParams;
        } else {
          config.data = allParams;
        }
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ Binance API error for ${endpoint}:`, error.message);

      // Retry logic for certain errors
      if (retryCount < this.config.retryAttempts) {
        const shouldRetry = this.shouldRetryRequest(error);

        if (shouldRetry) {
          console.log(
            `🔄 Retrying request to ${endpoint} (attempt ${retryCount + 1})`
          );
          await this.delay(this.config.retryDelay * (retryCount + 1));

          return this.makeRequest(endpoint, {
            ...options,
            retryCount: retryCount + 1,
          });
        }
      }

      throw this.handleApiError(error, endpoint);
    }
  }

  /**
   * Determine if request should be retried
   * @param {Error} error - Request error
   * @returns {boolean} Whether to retry
   */
  shouldRetryRequest(error) {
    // Retry on network errors, timeouts, and certain HTTP status codes
    if (!error.response) return true; // Network error

    const status = error.response.status;
    const retryableStatuses = [429, 500, 502, 503, 504]; // Rate limit, server errors

    return retryableStatuses.includes(status);
  }

  /**
   * Handle API errors and create meaningful error messages
   * @param {Error} error - Original error
   * @param {string} endpoint - API endpoint
   * @returns {Error} Formatted error
   */
  handleApiError(error, endpoint) {
    if (error.response) {
      const { status, data } = error.response;
      const message = data?.msg || data?.message || `HTTP ${status}`;
      return new Error(`Binance API error (${endpoint}): ${message}`);
    } else if (error.request) {
      return new Error(`Network error for ${endpoint}: ${error.message}`);
    } else {
      return new Error(`Request error for ${endpoint}: ${error.message}`);
    }
  }

  /**
   * Delay utility for retry logic
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Delay promise
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get kline/candlestick data
   * @param {Object} options - Kline options
   * @returns {Promise<Array>} Kline data
   */
  async getKlines(options = {}) {
    const {
      symbol = this.config.defaultSymbol,
      interval = "1h",
      limit = 500,
      startTime,
      endTime,
    } = options;

    const params = {
      symbol,
      interval,
      limit,
    };

    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;

    try {
      const data = await this.makeRequest("/api/v3/klines", { params });

      return data.map((kline) => ({
        openTime: kline[0],
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        closeTime: kline[6],
        quoteAssetVolume: parseFloat(kline[7]),
        numberOfTrades: kline[8],
        takerBuyBaseAssetVolume: parseFloat(kline[9]),
        takerBuyQuoteAssetVolume: parseFloat(kline[10]),
      }));
    } catch (error) {
      console.error(`❌ Failed to fetch klines for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get 24hr ticker price change statistics
   * @param {string} symbol - Trading symbol
   * @returns {Promise<Object>} Ticker data
   */
  async getTicker24hr(symbol = this.config.defaultSymbol) {
    try {
      return await this.makeRequest("/api/v3/ticker/24hr", {
        params: { symbol },
      });
    } catch (error) {
      console.error(
        `❌ Failed to fetch 24hr ticker for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get current average price
   * @param {string} symbol - Trading symbol
   * @returns {Promise<Object>} Price data
   */
  async getAvgPrice(symbol = this.config.defaultSymbol) {
    try {
      return await this.makeRequest("/api/v3/avgPrice", {
        params: { symbol },
      });
    } catch (error) {
      console.error(
        `❌ Failed to fetch average price for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get order book depth
   * @param {string} symbol - Trading symbol
   * @param {number} limit - Limit of orders to return
   * @returns {Promise<Object>} Order book data
   */
  async getOrderBook(symbol = this.config.defaultSymbol, limit = 100) {
    try {
      return await this.makeRequest("/api/v3/depth", {
        params: { symbol, limit },
      });
    } catch (error) {
      console.error(
        `❌ Failed to fetch order book for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get account information (requires authentication)
   * @returns {Promise<Object>} Account data
   */
  async getAccountInfo() {
    try {
      return await this.makeRequest("/api/v3/account", {
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to fetch account info:", error.message);
      throw error;
    }
  }

  /**
   * Get trading fees for symbol (requires authentication)
   * @param {string} symbol - Trading symbol
   * @returns {Promise<Object>} Trading fees
   */
  async getTradingFees(symbol = this.config.defaultSymbol) {
    try {
      return await this.makeRequest("/api/v3/tradeFee", {
        params: { symbol },
        authenticated: true,
      });
    } catch (error) {
      console.error(
        `❌ Failed to fetch trading fees for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Test connectivity to the API
   * @returns {Promise<Object>} Ping response
   */
  async ping() {
    try {
      return await this.makeRequest("/api/v3/ping");
    } catch (error) {
      console.error("❌ Failed to ping Binance API:", error.message);
      throw error;
    }
  }

  /**
   * Get server time
   * @returns {Promise<Object>} Server time
   */
  async getServerTime() {
    try {
      return await this.makeRequest("/api/v3/time");
    } catch (error) {
      console.error("❌ Failed to get server time:", error.message);
      throw error;
    }
  }

  /**
   * Get exchange information
   * @returns {Promise<Object>} Exchange info
   */
  async getExchangeInfo() {
    try {
      return await this.makeRequest("/api/v3/exchangeInfo");
    } catch (error) {
      console.error("❌ Failed to get exchange info:", error.message);
      throw error;
    }
  }

  /**
   * Place a new order (requires authentication)
   * @param {Object} orderParams - Order parameters
   * @returns {Promise<Object>} Order response
   */
  async placeOrder(orderParams) {
    const {
      symbol = this.config.defaultSymbol,
      side, // 'BUY' or 'SELL'
      type, // 'MARKET', 'LIMIT', 'STOP_LOSS', etc.
      quantity,
      price,
      timeInForce = "GTC", // Good Till Canceled
      stopPrice,
      newClientOrderId,
    } = orderParams;

    if (!side || !type || !quantity) {
      throw new Error(
        "Missing required order parameters: side, type, quantity"
      );
    }

    const params = {
      symbol,
      side,
      type,
      quantity,
    };

    // Add optional parameters based on order type
    if (type === "LIMIT") {
      if (!price) throw new Error("Price required for LIMIT orders");
      params.price = price;
      params.timeInForce = timeInForce;
    }

    if (type === "STOP_LOSS" || type === "STOP_LOSS_LIMIT") {
      if (!stopPrice) throw new Error("Stop price required for stop orders");
      params.stopPrice = stopPrice;
    }

    if (newClientOrderId) {
      params.newClientOrderId = newClientOrderId;
    }

    try {
      console.log(
        `📝 Placing ${side} order for ${quantity} ${symbol} at ${
          price || "market"
        }`
      );

      return await this.makeRequest("/api/v3/order", {
        method: "POST",
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to place order:", error.message);
      throw error;
    }
  }

  /**
   * Cancel an existing order (requires authentication)
   * @param {Object} cancelParams - Cancel parameters
   * @returns {Promise<Object>} Cancel response
   */
  async cancelOrder(cancelParams) {
    const {
      symbol = this.config.defaultSymbol,
      orderId,
      origClientOrderId,
    } = cancelParams;

    if (!orderId && !origClientOrderId) {
      throw new Error("Either orderId or origClientOrderId is required");
    }

    const params = { symbol };
    if (orderId) params.orderId = orderId;
    if (origClientOrderId) params.origClientOrderId = origClientOrderId;

    try {
      console.log(
        `❌ Canceling order ${orderId || origClientOrderId} for ${symbol}`
      );

      return await this.makeRequest("/api/v3/order", {
        method: "DELETE",
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to cancel order:", error.message);
      throw error;
    }
  }

  /**
   * Get order status (requires authentication)
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Object>} Order data
   */
  async getOrder(queryParams) {
    const {
      symbol = this.config.defaultSymbol,
      orderId,
      origClientOrderId,
    } = queryParams;

    if (!orderId && !origClientOrderId) {
      throw new Error("Either orderId or origClientOrderId is required");
    }

    const params = { symbol };
    if (orderId) params.orderId = orderId;
    if (origClientOrderId) params.origClientOrderId = origClientOrderId;

    try {
      return await this.makeRequest("/api/v3/order", {
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to get order:", error.message);
      throw error;
    }
  }

  /**
   * Get all open orders (requires authentication)
   * @param {string} symbol - Trading symbol (optional)
   * @returns {Promise<Array>} Open orders
   */
  async getOpenOrders(symbol = null) {
    const params = {};
    if (symbol) params.symbol = symbol;

    try {
      return await this.makeRequest("/api/v3/openOrders", {
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to get open orders:", error.message);
      throw error;
    }
  }

  /**
   * Get order history (requires authentication)
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Array>} Order history
   */
  async getOrderHistory(queryParams = {}) {
    const {
      symbol = this.config.defaultSymbol,
      limit = 500,
      startTime,
      endTime,
    } = queryParams;

    const params = { symbol, limit };
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;

    try {
      return await this.makeRequest("/api/v3/allOrders", {
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to get order history:", error.message);
      throw error;
    }
  }

  /**
   * Get trade history (requires authentication)
   * @param {Object} queryParams - Query parameters
   * @returns {Promise<Array>} Trade history
   */
  async getTradeHistory(queryParams = {}) {
    const {
      symbol = this.config.defaultSymbol,
      limit = 500,
      startTime,
      endTime,
    } = queryParams;

    const params = { symbol, limit };
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;

    try {
      return await this.makeRequest("/api/v3/myTrades", {
        params,
        authenticated: true,
      });
    } catch (error) {
      console.error("❌ Failed to get trade history:", error.message);
      throw error;
    }
  }

  /**
   * Get current balances (requires authentication)
   * @returns {Promise<Array>} Account balances
   */
  async getBalances() {
    try {
      const accountInfo = await this.getAccountInfo();
      return accountInfo.balances.filter(
        (balance) =>
          parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0
      );
    } catch (error) {
      console.error("❌ Failed to get balances:", error.message);
      throw error;
    }
  }

  /**
   * Get balance for specific asset (requires authentication)
   * @param {string} asset - Asset symbol (e.g., 'BTC', 'ETH', 'USDT')
   * @returns {Promise<Object>} Asset balance
   */
  async getAssetBalance(asset) {
    try {
      const balances = await this.getBalances();
      return (
        balances.find((balance) => balance.asset === asset) || {
          asset,
          free: "0.********",
          locked: "0.********",
        }
      );
    } catch (error) {
      console.error(`❌ Failed to get balance for ${asset}:`, error.message);
      throw error;
    }
  }

  /**
   * Check if API credentials are configured
   * @returns {boolean} Whether credentials are available
   */
  hasCredentials() {
    return !!(this.config.apiKey && this.config.apiSecret);
  }

  /**
   * Test API connectivity and authentication
   * @returns {Promise<Object>} Test results
   */
  async testConnection() {
    const results = {
      ping: false,
      serverTime: false,
      authentication: false,
      timestamp: new Date().toISOString(),
    };

    try {
      // Test basic connectivity
      await this.ping();
      results.ping = true;
      console.log("✅ Binance API ping successful");

      // Test server time
      await this.getServerTime();
      results.serverTime = true;
      console.log("✅ Binance server time retrieved");

      // Test authentication if credentials available
      if (this.hasCredentials()) {
        await this.getAccountInfo();
        results.authentication = true;
        console.log("✅ Binance authentication successful");
      } else {
        console.log("⚠️ No API credentials configured - skipping auth test");
      }
    } catch (error) {
      console.error("❌ Binance connection test failed:", error.message);
    }

    return results;
  }
}
