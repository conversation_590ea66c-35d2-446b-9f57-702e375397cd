import axios from "axios";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { Chart, registerables } from "chart.js";
import { EMA, RSI, WMA } from "technicalindicators";

// Register Chart.js components
Chart.register(...registerables);

/**
 * ChartHandler - Manages chart data retrieval, processing, caching, and generation
 * Responsibilities:
 * - Fetch market data from Binance API
 * - Cache data with TTL for performance
 * - Calculate technical indicators
 * - Generate charts for AI analysis
 * - Handle multi-timeframe data processing
 */
export class ChartHandler {
  constructor(config = {}) {
    this.config = {
      binanceApiUrl:
        config.binanceApiUrl || "https://api.binance.com/api/v3/klines",
      symbol: config.symbol || "ETHUSDT",
      timeframes: config.timeframes || {
        "15m": {
          interval: "15m",
          limit: 96,
          name: "15-minute",
          ttl: 15 * 60 * 1000,
        },
        "1h": {
          interval: "1h",
          limit: 168,
          name: "1-hour",
          ttl: 60 * 60 * 1000,
        },
        "4h": {
          interval: "4h",
          limit: 180,
          name: "4-hour",
          ttl: 4 * 60 * 60 * 1000,
        },
      },
      ...config,
    };

    // Data cache for multi-timeframe analysis
    this.dataCache = {};
    this.initializeCache();

    // Chart.js registration callback
    this.registerAll = this.createChartRegistration();
  }

  /**
   * Initialize cache structure for all timeframes
   */
  initializeCache() {
    Object.keys(this.config.timeframes).forEach((timeframe) => {
      this.dataCache[timeframe] = {
        data: null,
        lastUpdate: 0,
        ttl: this.config.timeframes[timeframe].ttl,
      };
    });
  }

  /**
   * Create Chart.js registration callback
   */
  createChartRegistration() {
    const symbol = this.config.symbol; // Capture symbol in closure

    return (Chart) => {
      Chart.register(...registerables);

      // Custom Candlestick Plugin
      const CandlestickPlugin = {
        id: "candlestick",
        afterDatasetsDraw(chart) {
          const { ctx, data, scales } = chart;
          const dataset = data.datasets.find(
            (ds) => ds.label && ds.label.includes(symbol)
          );
          if (!dataset || !dataset.data[0] || dataset.data[0].o === undefined)
            return;

          const xScale = scales.x;
          const yScale = scales.y;
          const candleWidth = Math.max(
            2,
            (xScale.width / dataset.data.length) * 0.8
          );

          dataset.data.forEach((candle, i) => {
            if (!candle || candle.o === undefined) return;

            const x = xScale.getPixelForValue(i);
            const yOpen = yScale.getPixelForValue(candle.o);
            const yClose = yScale.getPixelForValue(candle.c);
            const yHigh = yScale.getPixelForValue(candle.h);
            const yLow = yScale.getPixelForValue(candle.l);

            const isGreen = candle.c >= candle.o;
            ctx.strokeStyle = isGreen ? "#00ff88" : "#ff4444";
            ctx.fillStyle = isGreen ? "#00ff88" : "#ff4444";

            // Draw wick
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x, yHigh);
            ctx.lineTo(x, yLow);
            ctx.stroke();

            // Draw body
            const bodyHeight = Math.abs(yClose - yOpen);
            const bodyTop = Math.min(yOpen, yClose);

            if (bodyHeight < 1) {
              // Doji - draw thin line
              ctx.lineWidth = 1;
              ctx.beginPath();
              ctx.moveTo(x - candleWidth / 2, yOpen);
              ctx.lineTo(x + candleWidth / 2, yOpen);
              ctx.stroke();
            } else {
              // Normal candle
              ctx.fillRect(
                x - candleWidth / 2,
                bodyTop,
                candleWidth,
                bodyHeight
              );
            }
          });
        },
      };

      Chart.register(CandlestickPlugin);
    };
  }

  /**
   * Fetch market data for a specific timeframe
   * @param {string} timeframe - The timeframe to fetch (15m, 1h, 4h)
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchTimeframeData(timeframe) {
    const config = this.config.timeframes[timeframe];
    if (!config) {
      throw new Error(`Invalid timeframe: ${timeframe}`);
    }

    try {
      const { data } = await axios.get(this.config.binanceApiUrl, {
        params: {
          symbol: this.config.symbol,
          interval: config.interval,
          limit: config.limit,
        },
        timeout: 10000, // 10 second timeout
      });

      return data.map((d) => ({
        time: d[0],
        open: parseFloat(d[1]),
        high: parseFloat(d[2]),
        low: parseFloat(d[3]),
        close: parseFloat(d[4]),
        volume: parseFloat(d[5]),
        closeTime: d[6],
        timeframe: timeframe,
      }));
    } catch (error) {
      console.error(`❌ Error fetching ${timeframe} data:`, error.message);
      throw new Error(`Failed to fetch ${timeframe} data: ${error.message}`);
    }
  }

  /**
   * Get cached data or fetch fresh data if cache is expired
   * @param {string} timeframe - The timeframe to get data for
   * @returns {Promise<Array>} Array of candle data
   */
  async getCachedData(timeframe) {
    const cache = this.dataCache[timeframe];
    if (!cache) {
      throw new Error(`Invalid timeframe: ${timeframe}`);
    }

    const now = Date.now();

    // Check if cache is valid
    if (cache.data && now - cache.lastUpdate < cache.ttl) {
      console.log(`📋 Using cached data for ${timeframe}`);
      return cache.data;
    }

    // Fetch fresh data
    console.log(`🔄 Fetching fresh data for ${timeframe}`);
    const data = await this.fetchTimeframeData(timeframe);

    // Update cache
    cache.data = data;
    cache.lastUpdate = now;

    return data;
  }

  /**
   * Fetch data for all configured timeframes
   * @returns {Promise<Object>} Object with timeframe keys and candle data arrays
   */
  async fetchAllTimeframes() {
    try {
      const timeframes = Object.keys(this.config.timeframes);
      const promises = timeframes.map((tf) => this.getCachedData(tf));
      const results = await Promise.all(promises);

      const data = {};
      timeframes.forEach((tf, index) => {
        data[tf] = results[index];
      });

      return data;
    } catch (error) {
      console.error("❌ Error fetching multi-timeframe data:", error);
      throw error;
    }
  }

  /**
   * Calculate technical indicators for candle data
   * @param {Array} candles - Array of candle data
   * @returns {Object} Object containing calculated indicators
   */
  calculateIndicators(candles) {
    if (!candles || candles.length === 0) {
      throw new Error("No candle data provided for indicator calculation");
    }

    const len = candles.length;
    const closes = candles.map((c) => c.close);
    const highs = candles.map((c) => c.high);
    const lows = candles.map((c) => c.low);
    const volumes = candles.map((c) => c.volume);

    // Helper function to pad arrays to same length
    const padToLen = (arr, targetLen) => {
      const padding = new Array(targetLen - arr.length).fill(null);
      return [...padding, ...arr];
    };

    try {
      // EMA calculations
      const ema20 = padToLen(
        EMA.calculate({ values: closes, period: 20 }),
        len
      );
      const ema50 = padToLen(
        EMA.calculate({ values: closes, period: 50 }),
        len
      );
      const ema89 = padToLen(
        EMA.calculate({ values: closes, period: 89 }),
        len
      );
      const ema200 = padToLen(
        EMA.calculate({ values: closes, period: 200 }),
        len
      );
      const ema610 = padToLen(
        EMA.calculate({ values: closes, period: 610 }),
        len
      );

      // RSI calculations
      const rsi14 = RSI.calculate({ values: closes, period: 14 });
      const rsi = padToLen(rsi14, len);
      const rsiEma9 = padToLen(
        EMA.calculate({ values: rsi14, period: 9 }),
        len
      );
      const rsiWma45 = padToLen(
        WMA.calculate({ values: rsi14, period: 45 }),
        len
      );

      return {
        closes,
        highs,
        lows,
        volumes,
        ema20,
        ema50,
        ema89,
        ema200,
        ema610,
        rsi,
        rsiEma9,
        rsiWma45,
      };
    } catch (error) {
      console.error("❌ Error calculating indicators:", error);
      throw new Error(`Failed to calculate indicators: ${error.message}`);
    }
  }

  /**
   * Clear cache for a specific timeframe or all timeframes
   * @param {string} timeframe - Optional timeframe to clear, if not provided clears all
   */
  clearCache(timeframe = null) {
    if (timeframe) {
      if (this.dataCache[timeframe]) {
        this.dataCache[timeframe].data = null;
        this.dataCache[timeframe].lastUpdate = 0;
        console.log(`🗑️ Cleared cache for ${timeframe}`);
      }
    } else {
      Object.keys(this.dataCache).forEach((tf) => {
        this.dataCache[tf].data = null;
        this.dataCache[tf].lastUpdate = 0;
      });
      console.log("🗑️ Cleared all cache");
    }
  }

  /**
   * Get cache status for all timeframes
   * @returns {Object} Cache status information
   */
  getCacheStatus() {
    const status = {};
    const now = Date.now();

    Object.keys(this.dataCache).forEach((timeframe) => {
      const cache = this.dataCache[timeframe];
      const isValid = cache.data && now - cache.lastUpdate < cache.ttl;
      const ageMinutes = cache.lastUpdate
        ? Math.floor((now - cache.lastUpdate) / 60000)
        : null;

      status[timeframe] = {
        hasData: !!cache.data,
        isValid,
        ageMinutes,
        ttlMinutes: Math.floor(cache.ttl / 60000),
      };
    });

    return status;
  }

  /**
   * Generate a clean chart optimized for AI analysis
   * @param {Array} candles - Candle data
   * @param {Object} indicators - Technical indicators
   * @param {string} timeframe - Timeframe label
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @returns {Promise<Buffer>} Chart image buffer
   */
  async generateCleanChartForAI(
    candles,
    indicators,
    timeframe,
    width = 1200,
    height = 800
  ) {
    const canvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour: "#FFFFFF", // White background for better AI analysis
      chartCallback: this.registerAll,
    });

    const candleData = candles.map((c, i) => ({
      x: i,
      o: c.open,
      h: c.high,
      l: c.low,
      c: c.close,
      time: c.time,
    }));

    const config = {
      type: "line",
      data: {
        labels: candles.map((_, i) => i),
        datasets: [
          {
            label: `${this.config.symbol} ${timeframe}`,
            data: candleData,
            borderColor: "transparent",
            backgroundColor: "transparent",
          },
          {
            label: "EMA 20",
            data: indicators.ema20,
            borderColor: "#22c55e",
            backgroundColor: "transparent",
            borderWidth: 2,
            pointRadius: 0,
            tension: 0.1,
          },
          {
            label: "EMA 50",
            data: indicators.ema50,
            borderColor: "#f97316",
            backgroundColor: "transparent",
            borderWidth: 2,
            pointRadius: 0,
            tension: 0.1,
          },
          {
            label: "EMA 200",
            data: indicators.ema200,
            borderColor: "#ef4444",
            backgroundColor: "transparent",
            borderWidth: 2,
            pointRadius: 0,
            tension: 0.1,
          },
        ],
      },
      options: {
        responsive: false,
        plugins: {
          legend: {
            display: true,
            position: "top",
            labels: {
              color: "#000000",
              font: { size: 12 },
            },
          },
          title: {
            display: true,
            text: `${this.config.symbol} - ${timeframe} Analysis`,
            color: "#000000",
            font: { size: 16, weight: "bold" },
          },
        },
        scales: {
          x: {
            display: true,
            grid: { color: "#e5e7eb" },
            ticks: { color: "#000000" },
          },
          y: {
            display: true,
            grid: { color: "#e5e7eb" },
            ticks: { color: "#000000" },
          },
        },
        elements: {
          point: { radius: 0 },
        },
      },
    };

    return canvas.renderToBuffer(config, "image/png");
  }

  /**
   * Generate charts for multiple timeframes optimized for AI analysis
   * @param {Object} multiTimeframeAnalysis - Analysis data for all timeframes
   * @returns {Promise<Object>} Object with timeframe keys and chart buffers
   */
  async generateMultiTimeframeChartsForAI(multiTimeframeAnalysis) {
    console.log("📊 Generating AI-optimized charts for visual analysis...");

    const charts = {};
    const chartPromises = [];

    for (const [timeframe, data] of Object.entries(multiTimeframeAnalysis)) {
      const promise = this.generateCleanChartForAI(
        data.candles,
        data.indicators,
        timeframe
      )
        .then((buffer) => {
          charts[timeframe] = { buffer };
          console.log(
            `✅ Generated ${timeframe} chart: ai-chart-${timeframe}.png`
          );
        })
        .catch((error) => {
          console.error(`❌ Failed to generate ${timeframe} chart:`, error);
          throw error;
        });

      chartPromises.push(promise);
    }

    await Promise.all(chartPromises);
    return charts;
  }
}
