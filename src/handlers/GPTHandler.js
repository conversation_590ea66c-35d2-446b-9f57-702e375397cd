import OpenAI from "openai";

/**
 * <PERSON><PERSON><PERSON><PERSON> - Manages all interactions with OpenAI/GPT API
 * Responsibilities:
 * - Chart analysis using GPT-4 Vision
 * - Trading signal generation
 * - Text-based market analysis
 * - JSON parsing and validation
 * - Fallback analysis creation
 */
export class GPTHandler {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
      models: {
        vision: config.visionModel || "gpt-4o",
        text: config.textModel || "gpt-4o-mini",
      },
      symbol: config.symbol || "ETHUSDT",
      maxTokens: config.maxTokens || 2000,
      temperature: config.temperature || 0.1,
      ...config,
    };

    if (!this.config.apiKey) {
      throw new Error("OpenAI API key is required");
    }

    this.openai = new OpenAI({ apiKey: this.config.apiKey });
  }

  /**
   * Analyze chart using GPT-4 Vision API
   * @param {Buffer} chartBuffer - Chart image buffer
   * @param {string} timeframe - Timeframe label
   * @param {number} currentPrice - Current price for context
   * @returns {Promise<Object>} Analysis result
   */
  async analyzeChartWithAIVision(chartBuffer, timeframe, currentPrice) {
    try {
      console.log(`🔍 Analyzing ${timeframe} chart with AI vision...`);

      // Convert buffer to base64 for OpenAI Vision API
      const base64Image = chartBuffer.toString("base64");

      const prompt = this.createVisionAnalysisPrompt(timeframe, currentPrice);

      const response = await this.openai.chat.completions.create({
        model: this.config.models.vision,
        messages: [
          {
            role: "system",
            content:
              "You are a technical analysis expert. You MUST respond ONLY with valid JSON. Do not include any explanatory text, apologies, or markdown formatting. Start your response with { and end with }. If you cannot analyze the chart, still respond with valid JSON using placeholder values.",
          },
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/png;base64,${base64Image}`,
                  detail: "high",
                },
              },
            ],
          },
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      });

      const analysisText = response.choices[0].message.content;
      return this.parseAIResponse(analysisText, timeframe);
    } catch (error) {
      console.error(`❌ AI vision analysis failed for ${timeframe}:`, error);
      return this.createFallbackAnalysis(timeframe);
    }
  }

  /**
   * Create vision analysis prompt
   * @param {string} timeframe - Timeframe label
   * @param {number} currentPrice - Current price
   * @returns {string} Formatted prompt
   */
  createVisionAnalysisPrompt(timeframe, currentPrice) {
    return `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy phân tích biểu đồ giá ${timeframe} của ${this.config.symbol} cho mục đích học tập và nghiên cứu.

BỐI CẢNH PHÂN TÍCH:
- Mã tài sản: ${this.config.symbol}
- Khung thời gian: ${timeframe}
- Giá tham khảo: ${currentPrice}

MỤC TIÊU PHÂN TÍCH GIÁO DỤC:
1. XU HƯỚNG GIÁ: Nhận dạng hướng chuyển động giá (tăng, giảm, hoặc ngang)
2. MÔ HÌNH KỸ THUẬT: Tìm các mô hình biểu đồ cổ điển để học tập
3. CẤU TRÚC NẾN: Phân tích các hình thành nến cho mục đích giáo dục
4. VÙNG GIÁ QUAN TRỌNG: Xác định các mức giá có ý nghĩa lịch sử
5. ĐƯỜNG TRUNG BÌNH: Quan sát mối quan hệ giá với các đường EMA (Xanh=EMA20, Cam=EMA50, Đỏ=EMA200)
6. KHỐI LƯỢNG: Nhận xét về hoạt động giao dịch nếu quan sát được
7. ĐỘNG NĂNG: Đánh giá sự thay đổi trong động năng giá

Đây là phân tích cho mục đích giáo dục và nghiên cứu, không phải lời khuyên đầu tư.

TRẢ LỜI theo định dạng JSON CHÍNH XÁC này:
{
  "trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Brief explanation of trend analysis"
  },
  "patterns": {
    "chart_patterns": ["list of identified chart patterns"],
    "candlestick_patterns": ["list of candlestick patterns"],
    "pattern_significance": "HIGH|MEDIUM|LOW"
  },
  "levels": {
    "support": [list of support price levels],
    "resistance": [list of resistance price levels],
    "key_level_proximity": "How close is current price to key levels"
  },
  "moving_averages": {
    "price_vs_ema20": "ABOVE|BELOW|AT",
    "price_vs_ema50": "ABOVE|BELOW|AT",
    "price_vs_ema200": "ABOVE|BELOW|AT",
    "ema_alignment": "BULLISH|BEARISH|MIXED",
    "ema_analysis": "Brief analysis of EMA positioning"
  },
  "momentum": {
    "direction": "INCREASING|DECREASING|NEUTRAL",
    "strength": 1-10,
    "momentum_analysis": "Analysis of momentum indicators"
  },
  "trading_signal": {
    "signal": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL",
    "entry_zone": "Suggested entry price range",
    "stop_loss": "Suggested stop loss level",
    "take_profit": "Suggested take profit levels",
    "risk_reward": "Risk to reward ratio",
    "signal_reasoning": "Why this signal was generated"
  },
  "overall_assessment": {
    "market_condition": "TRENDING|RANGING|VOLATILE|CONSOLIDATING",
    "trade_quality": "HIGH|MEDIUM|LOW",
    "confidence_score": 1-10,
    "key_insights": "Most important observations"
  }
}

QUAN TRỌNG:
- Chỉ trả lời bằng JSON hợp lệ
- Bắt đầu bằng { và kết thúc bằng }
- Không có văn bản giải thích bên ngoài JSON
- Không sử dụng markdown hoặc code blocks
- Đảm bảo tất cả string được đặt trong dấu ngoặc kép`;
  }

  /**
   * Parse AI response and handle JSON extraction
   * @param {string} analysisText - Raw AI response
   * @param {string} timeframe - Timeframe for context
   * @returns {Object} Parsed analysis object
   */
  parseAIResponse(analysisText, timeframe) {
    try {
      let jsonText = analysisText.trim();

      console.log(
        `🔍 Raw AI response for ${timeframe}: ${analysisText.substring(
          0,
          100
        )}...`
      );

      // Look for JSON block if wrapped in markdown
      const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonText = jsonMatch[1].trim();
        console.log(`📝 Found JSON in markdown block for ${timeframe}`);
      }

      // Look for JSON block without language specification
      const codeBlockMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch && !jsonMatch) {
        const potentialJson = codeBlockMatch[1].trim();
        if (potentialJson.startsWith("{") && potentialJson.endsWith("}")) {
          jsonText = potentialJson;
          console.log(`📝 Found JSON in code block for ${timeframe}`);
        }
      }

      // Remove any leading/trailing non-JSON text
      const jsonStart = jsonText.indexOf("{");
      const jsonEnd = jsonText.lastIndexOf("}");

      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
        console.log(`🔧 Extracted JSON substring for ${timeframe}`);
      } else {
        throw new Error(
          `No valid JSON structure found in response for ${timeframe}`
        );
      }

      // Additional cleaning for common AI response issues
      jsonText = jsonText
        .replace(/^\s*I'm sorry[^{]*/, "") // Remove "I'm sorry" prefixes
        .replace(/^[^{]*/, "") // Remove any text before first {
        .replace(/}[^}]*$/, "}") // Remove any text after last }
        .trim();

      if (!jsonText.startsWith("{") || !jsonText.endsWith("}")) {
        throw new Error(
          `Invalid JSON format for ${timeframe}: doesn't start with { or end with }`
        );
      }

      console.log(`🔍 Attempting to parse cleaned JSON for ${timeframe}...`);

      // Try to repair common JSON issues before parsing
      jsonText = this.repairCommonJsonIssues(jsonText);

      const analysis = JSON.parse(jsonText);

      // Validate required fields
      if (
        !analysis.trend ||
        !analysis.trading_signal ||
        !analysis.overall_assessment
      ) {
        throw new Error("Missing required fields in AI analysis");
      }

      console.log(`✅ Successfully parsed JSON for ${timeframe}`);
      return analysis;
    } catch (parseError) {
      console.error(
        `❌ Failed to parse AI analysis for ${timeframe}:`,
        parseError.message
      );
      console.error(
        `Raw response (first 300 chars): ${analysisText.substring(0, 300)}...`
      );

      // Log the cleaned JSON attempt for debugging
      if (parseError.message.includes("JSON")) {
        const jsonStart = analysisText.indexOf("{");
        const jsonEnd = analysisText.lastIndexOf("}");
        if (jsonStart !== -1 && jsonEnd !== -1) {
          const attemptedJson = analysisText.substring(jsonStart, jsonEnd + 1);
          console.error(
            `Attempted JSON parse: ${attemptedJson.substring(0, 200)}...`
          );
        }
      }

      // Try to create analysis from text if JSON parsing fails
      return this.createAnalysisFromText(analysisText, timeframe);
    }
  }

  /**
   * Repair common JSON formatting issues
   * @param {string} jsonText - JSON text to repair
   * @returns {string} Repaired JSON text
   */
  repairCommonJsonIssues(jsonText) {
    try {
      return (
        jsonText
          // Fix trailing commas
          .replace(/,(\s*[}\]])/g, "$1")
          // Fix missing quotes around property names
          .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
          // Fix single quotes to double quotes
          .replace(/'/g, '"')
          // Fix escaped quotes that shouldn't be escaped
          .replace(/\\"/g, '"')
          // Fix missing commas between properties
          .replace(/"\s*\n\s*"/g, '",\n"')
          // Remove any control characters
          .replace(/[\x00-\x1F\x7F]/g, "")
      );
    } catch (error) {
      console.warn("JSON repair failed:", error.message);
      return jsonText;
    }
  }

  /**
   * Create analysis from text when JSON parsing fails
   * @param {string} responseText - Raw response text
   * @param {string} timeframe - Timeframe for context
   * @returns {Object} Fallback analysis object
   */
  createAnalysisFromText(responseText, timeframe) {
    console.log(
      `🔄 Attempting to extract analysis from text for ${timeframe}...`
    );
    console.log(`Text response preview: ${responseText.substring(0, 150)}...`);

    // Check if response contains apology or error message
    const text = responseText.toLowerCase();
    const isApologyResponse =
      text.includes("i'm sorry") ||
      text.includes("i cannot") ||
      text.includes("unable to") ||
      text.includes("apologize");

    if (isApologyResponse) {
      console.log(
        `⚠️ AI provided apology response for ${timeframe}, using neutral fallback`
      );
      return this.createFallbackAnalysis(timeframe);
    }

    // Try to extract key information from text response
    // Determine trend from text
    let trendDirection = "SIDEWAYS";
    let trendStrength = 5;
    let confidence = 5;

    if (
      text.includes("bullish") ||
      text.includes("tăng") ||
      text.includes("uptrend") ||
      text.includes("up trend") ||
      text.includes("rising") ||
      text.includes("increasing") ||
      text.includes("positive") ||
      text.includes("strong buy")
    ) {
      trendDirection = "BULLISH";
      trendStrength = 7;
      confidence = 6;
    } else if (
      text.includes("bearish") ||
      text.includes("giảm") ||
      text.includes("downtrend") ||
      text.includes("down trend") ||
      text.includes("falling") ||
      text.includes("decreasing") ||
      text.includes("negative") ||
      text.includes("strong sell")
    ) {
      trendDirection = "BEARISH";
      trendStrength = 7;
      confidence = 6;
    }

    // Determine signal from text
    let signal = "HOLD";
    if (
      text.includes("buy") ||
      text.includes("long") ||
      text.includes("mua") ||
      text.includes("purchase") ||
      text.includes("enter long") ||
      text.includes("strong_buy") ||
      text.includes("strong buy")
    ) {
      signal = text.includes("strong") ? "STRONG_BUY" : "BUY";
    } else if (
      text.includes("sell") ||
      text.includes("short") ||
      text.includes("bán") ||
      text.includes("exit") ||
      text.includes("enter short") ||
      text.includes("strong_sell") ||
      text.includes("strong sell")
    ) {
      signal = text.includes("strong") ? "STRONG_SELL" : "SELL";
    }

    return {
      trend: {
        direction: trendDirection,
        strength: trendStrength,
        confidence: confidence,
        reasoning: `Extracted from text analysis for ${timeframe}`,
      },
      patterns: {
        chart_patterns: [],
        candlestick_patterns: [],
        pattern_significance: "LOW",
      },
      levels: {
        support: [],
        resistance: [],
        key_level_proximity: "Unknown",
      },
      moving_averages: {
        price_vs_ema20: "AT",
        price_vs_ema50: "AT",
        price_vs_ema200: "AT",
        ema_alignment: "MIXED",
        ema_analysis: "Text analysis - limited data",
      },
      momentum: {
        direction: "NEUTRAL",
        strength: 5,
        momentum_analysis: "Text analysis - limited data",
      },
      trading_signal: {
        signal: signal,
        entry_zone: "Market price",
        stop_loss: "Use 2% rule",
        take_profit: "Use 2:1 RR",
        risk_reward: "1:2",
        signal_reasoning: "Extracted from text analysis",
      },
      overall_assessment: {
        market_condition: "RANGING",
        trade_quality: "LOW",
        confidence_score: confidence,
        key_insights: `Text-based analysis for ${timeframe} - JSON parsing failed`,
      },
    };
  }

  /**
   * Create fallback analysis when all parsing fails
   * @param {string} timeframe - Timeframe for context
   * @returns {Object} Neutral fallback analysis
   */
  createFallbackAnalysis(timeframe) {
    return {
      trend: {
        direction: "SIDEWAYS",
        strength: 5,
        confidence: 3,
        reasoning: `AI analysis unavailable for ${timeframe}`,
      },
      patterns: {
        chart_patterns: [],
        candlestick_patterns: [],
        pattern_significance: "LOW",
      },
      levels: {
        support: [],
        resistance: [],
        key_level_proximity: "Unknown",
      },
      moving_averages: {
        price_vs_ema20: "AT",
        price_vs_ema50: "AT",
        price_vs_ema200: "AT",
        ema_alignment: "MIXED",
        ema_analysis: "Analysis unavailable",
      },
      momentum: {
        direction: "NEUTRAL",
        strength: 5,
        momentum_analysis: "Analysis unavailable",
      },
      trading_signal: {
        signal: "HOLD",
        entry_zone: "Wait for clear signal",
        stop_loss: "Use standard risk management",
        take_profit: "Use standard targets",
        risk_reward: "1:2",
        signal_reasoning: "Fallback analysis - no clear signal",
      },
      overall_assessment: {
        market_condition: "UNCERTAIN",
        trade_quality: "LOW",
        confidence_score: 3,
        key_insights: `Fallback analysis for ${timeframe} - AI analysis failed`,
      },
    };
  }

  /**
   * Generate AI-powered trading signal from multi-timeframe analysis
   * @param {Object} aiAnalysis - AI analysis results for all timeframes
   * @param {Object} multiTimeframeAnalysis - Raw market data for all timeframes
   * @returns {Promise<Object>} Trading signal object
   */
  async generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
    try {
      console.log("🎯 Generating AI-powered trading signal...");

      const { "4h": ai4h, "1h": ai1h, "15m": ai15m } = aiAnalysis;
      const currentPrice = multiTimeframeAnalysis["15m"].candles.at(-1).close;

      const multiTimeframePrompt = this.createTradingSignalPrompt(
        ai4h,
        ai1h,
        ai15m,
        currentPrice
      );

      const response = await this.openai.chat.completions.create({
        model: this.config.models.vision,
        messages: [
          {
            role: "system",
            content:
              "Bạn là chuyên gia phân tích kỹ thuật giáo dục với hơn 20 năm kinh nghiệm. Tạo phân tích giáo dục thận trọng, có lý lẽ dựa trên đa khung thời gian cho mục đích học tập. Luôn nhấn mạnh quản lý rủi ro và tính chất giáo dục của phân tích.",
          },
          {
            role: "user",
            content: multiTimeframePrompt,
          },
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      });

      const signalText = response.choices[0].message.content;
      return this.parseTradingSignal(signalText);
    } catch (error) {
      console.error("❌ Failed to generate AI trading signal:", error);
      return this.createFallbackTradingSignal();
    }
  }

  /**
   * Create trading signal prompt for multi-timeframe analysis
   * @param {Object} ai4h - 4h analysis
   * @param {Object} ai1h - 1h analysis
   * @param {Object} ai15m - 15m analysis
   * @param {number} currentPrice - Current price
   * @returns {string} Formatted prompt
   */
  createTradingSignalPrompt(ai4h, ai1h, ai15m, currentPrice) {
    return `Bạn là chuyên gia phân tích kỹ thuật giáo dục. Hãy tạo một phân tích giáo dục dựa trên dữ liệu đa khung thời gian cho mục đích học tập và nghiên cứu.

PHÂN TÍCH ĐA KHUNG THỜI GIAN CHO ${this.config.symbol}:

📊 KHUNG 4H (Xu hướng chính):
- Hướng: ${ai4h.trend.direction} (Độ mạnh: ${ai4h.trend.strength}/10)
- Tín hiệu: ${ai4h.trading_signal.signal}
- Đánh giá: ${ai4h.overall_assessment.market_condition}

📊 KHUNG 1H (Xu hướng trung hạn):
- Hướng: ${ai1h.trend.direction} (Độ mạnh: ${ai1h.trend.strength}/10)
- Tín hiệu: ${ai1h.trading_signal.signal}
- Đánh giá: ${ai1h.overall_assessment.market_condition}

📊 KHUNG 15M (Entry timing):
- Hướng: ${ai15m.trend.direction} (Độ mạnh: ${ai15m.trend.strength}/10)
- Tín hiệu: ${ai15m.trading_signal.signal}
- Đánh giá: ${ai15m.overall_assessment.market_condition}

💰 GIÁ HIỆN TẠI: $${currentPrice}

Hãy tạo một phân tích tổng hợp cho mục đích giáo dục theo format JSON:

{
  "multi_timeframe_analysis": {
    "primary_trend": "BULLISH|BEARISH|SIDEWAYS",
    "trend_alignment": "ALIGNED|MIXED|CONFLICTING",
    "overall_strength": 1-10,
    "key_confluence": "Mô tả sự hội tụ của các khung thời gian"
  },
  "trading_recommendation": {
    "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL",
    "confidence": 1-10,
    "entry_strategy": "Chiến lược vào lệnh cụ thể",
    "risk_management": "Quản lý rủi ro chi tiết"
  },
  "price_targets": {
    "entry_zone": "Vùng giá vào lệnh",
    "stop_loss": "Mức cắt lỗ",
    "take_profit_1": "Mục tiêu lợi nhuận 1",
    "take_profit_2": "Mục tiêu lợi nhuận 2",
    "risk_reward_ratio": "Tỷ lệ rủi ro/lợi nhuận"
  },
  "market_context": {
    "dominant_timeframe": "4H|1H|15M",
    "market_phase": "ACCUMULATION|MARKUP|DISTRIBUTION|MARKDOWN",
    "volatility_assessment": "HIGH|MEDIUM|LOW",
    "trade_quality": "HIGH|MEDIUM|LOW"
  },
  "educational_insights": {
    "key_learning_points": "Điểm học tập chính",
    "pattern_recognition": "Nhận dạng mô hình",
    "risk_factors": "Các yếu tố rủi ro cần lưu ý"
  },
  "overall_assessment": {
    "final_confidence": 1-10,
    "key_message": "Thông điệp chính của phân tích",
    "next_review": "Khi nào nên xem xét lại"
  },
  "metadata": {
    "analysis_timestamp": "${new Date().toISOString()}",
    "symbol": "${this.config.symbol}",
    "current_price": ${currentPrice},
    "timeframes_analyzed": ["4h", "1h", "15m"]
  }
}

QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Xem xét tất cả khung thời gian nhưng ưu tiên xu hướng khung thời gian cao hơn cho hướng chính.`;
  }

  /**
   * Parse trading signal response
   * @param {string} signalText - Raw signal response
   * @returns {Object} Parsed trading signal
   */
  parseTradingSignal(signalText) {
    try {
      console.log("🔍 Attempting to parse AI trading signal...");

      // Clean and extract JSON
      let jsonText = signalText.trim();
      const jsonMatch =
        jsonText.match(/```json\s*([\s\S]*?)\s*```/) ||
        jsonText.match(/```\s*([\s\S]*?)\s*```/);

      if (jsonMatch) {
        jsonText = jsonMatch[1].trim();
      }

      const jsonStart = jsonText.indexOf("{");
      const jsonEnd = jsonText.lastIndexOf("}");

      if (jsonStart !== -1 && jsonEnd !== -1) {
        jsonText = jsonText.substring(jsonStart, jsonEnd + 1);
      }

      const signal = JSON.parse(jsonText);
      console.log("✅ AI trading signal generated successfully");
      return signal;
    } catch (error) {
      console.error("❌ Failed to parse trading signal:", error);
      return this.createFallbackTradingSignal();
    }
  }

  /**
   * Create fallback trading signal
   * @returns {Object} Fallback trading signal
   */
  createFallbackTradingSignal() {
    return {
      multi_timeframe_analysis: {
        primary_trend: "SIDEWAYS",
        trend_alignment: "MIXED",
        overall_strength: 5,
        key_confluence: "Analysis unavailable - using fallback",
      },
      trading_recommendation: {
        action: "HOLD",
        confidence: 3,
        entry_strategy: "Wait for clearer signals",
        risk_management: "Use standard 2% risk per trade",
      },
      price_targets: {
        entry_zone: "Current market price",
        stop_loss: "2% below entry",
        take_profit_1: "2% above entry",
        take_profit_2: "4% above entry",
        risk_reward_ratio: "1:2",
      },
      market_context: {
        dominant_timeframe: "UNKNOWN",
        market_phase: "UNCERTAIN",
        volatility_assessment: "MEDIUM",
        trade_quality: "LOW",
      },
      educational_insights: {
        key_learning_points: "AI analysis failed - manual review needed",
        pattern_recognition: "Unable to identify patterns",
        risk_factors: "High uncertainty due to analysis failure",
      },
      overall_assessment: {
        final_confidence: 3,
        key_message: "Analysis unavailable - exercise caution",
        next_review: "When AI analysis is restored",
      },
      metadata: {
        analysis_timestamp: new Date().toISOString(),
        symbol: this.config.symbol,
        current_price: 0,
        timeframes_analyzed: ["fallback"],
      },
    };
  }
}
