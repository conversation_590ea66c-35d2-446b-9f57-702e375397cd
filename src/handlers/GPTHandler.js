import OpenAI from "openai";

/**
 * GP<PERSON>andler class manages all interactions with the OpenAI GPT API
 * for trading signal generation and market analysis.
 */
export class GPTHandler {
  constructor(apiKey, model = "gpt-4o-mini") {
    if (!apiKey) {
      throw new Error("OpenAI API key is required");
    }
    
    this.openai = new OpenAI({ apiKey });
    this.model = model;
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
  }

  /**
   * Delays execution for the specified number of milliseconds
   * @private
   * @param {number} ms - Milliseconds to delay
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Makes a request to the OpenAI API with retry logic
   * @private
   * @param {Array} messages - Array of message objects
   * @param {number} retryCount - Current retry attempt
   * @returns {Promise<string>} GPT response content
   */
  async _makeRequest(messages, retryCount = 0) {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
      });

      if (!response.choices || response.choices.length === 0) {
        throw new Error("No response choices received from OpenAI");
      }

      return response.choices[0].message.content;
    } catch (error) {
      if (retryCount < this.maxRetries) {
        console.warn(`GPT request failed (attempt ${retryCount + 1}/${this.maxRetries + 1}): ${error.message}`);
        await this._delay(this.retryDelay * (retryCount + 1));
        return this._makeRequest(messages, retryCount + 1);
      }
      throw new Error(`GPT request failed after ${this.maxRetries + 1} attempts: ${error.message}`);
    }
  }

  /**
   * Analyzes market data and generates trading signals
   * @param {Object} marketData - Market data object
   * @param {Object} marketData.candle - Latest candle data
   * @param {Object} marketData.indicators - Technical indicators
   * @param {string} marketData.symbol - Trading symbol
   * @param {string} marketData.interval - Time interval
   * @returns {Promise<string>} Trading analysis in HTML format
   */
  async analyzeMarket(marketData) {
    try {
      const { candle, indicators, symbol, interval } = marketData;

      if (!candle || !indicators || !symbol || !interval) {
        throw new Error("Missing required market data fields");
      }

      const analysisPrompt = this._buildAnalysisPrompt(marketData);
      
      const messages = [
        {
          role: "system",
          content: this._getSystemPrompt(),
        },
        {
          role: "user",
          content: analysisPrompt,
        },
      ];

      return await this._makeRequest(messages);
    } catch (error) {
      throw new Error(`Failed to analyze market: ${error.message}`);
    }
  }

  /**
   * Builds the analysis prompt for GPT
   * @private
   * @param {Object} marketData - Market data object
   * @returns {string} Formatted prompt
   */
  _buildAnalysisPrompt(marketData) {
    const { candle, indicators, symbol, interval } = marketData;

    return `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${symbol} khung ${interval} với dữ liệu:
- Giá hiện tại: ${candle.close}
- EMA20/50/89: ${[
      indicators.ema20?.at(-1),
      indicators.ema50?.at(-1),
      indicators.ema89?.at(-1)
    ]
      .map((v) => v?.toFixed(2) || "N/A")
      .join("/")}
- RSI(14): ${indicators.rsi?.at(-1)?.toFixed(1) || "N/A"}
- Sonic R PAC: ${indicators.pacC?.at(-1)?.toFixed(2) || "N/A"}
- Volume: ${candle.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]

<b>🎯 KỊCH BẢN TRADE:</b>
• <b>Loại:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Điều kiện entry cụ thể]
• <b>Xác nhận:</b> [Tín hiệu cần chờ]
• <b>Entry/SL/TP:</b> Entry [giá], SL [giá], TP [giá] (RR 1:[tỷ lệ])
• <b>Invalidation:</b> [Điều kiện hủy kèo]

<b>💡 Độ tin cậy:</b> [X]%

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;
  }

  /**
   * Gets the system prompt for trading analysis
   * @private
   * @returns {string} System prompt
   */
  _getSystemPrompt() {
    return `Bạn là chuyên gia trading với 10+ năm kinh nghiệm. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. 

Nguyên tắc phân tích:
1. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown
2. Phân tích dựa trên xu hướng EMA, RSI, và Sonic R PAC
3. Đưa ra giá entry/SL/TP cụ thể, không mơ hồ
4. Tính toán Risk/Reward ratio hợp lý (tối thiểu 1:2)
5. Ngắn gọn, chuyên nghiệp, dưới 600 ký tự
6. Độ tin cậy dựa trên sự đồng thuận của các chỉ báo

Quy tắc trading:
- LONG khi: EMA20 > EMA50, RSI > 50, giá trên PAC
- SHORT khi: EMA20 < EMA50, RSI < 50, giá dưới PAC  
- NO TRADE khi: tín hiệu mâu thuẫn hoặc sideway`;
  }

  /**
   * Generates a market summary for a specific timeframe
   * @param {Object} marketData - Market data object
   * @param {string} timeframe - Analysis timeframe (e.g., "short-term", "medium-term")
   * @returns {Promise<string>} Market summary
   */
  async generateMarketSummary(marketData, timeframe = "short-term") {
    try {
      const { candle, indicators, symbol, interval } = marketData;

      const messages = [
        {
          role: "system",
          content: `Bạn là chuyên gia phân tích thị trường. Tạo tóm tắt ${timeframe} cho ${symbol}.`,
        },
        {
          role: "user",
          content: `Tóm tắt thị trường ${symbol} (${interval}):
- Giá: ${candle.close}
- EMA20: ${indicators.ema20?.at(-1)?.toFixed(2) || "N/A"}
- RSI: ${indicators.rsi?.at(-1)?.toFixed(1) || "N/A"}
- Volume: ${candle.volume}

Viết tóm tắt ngắn gọn về xu hướng và điểm chú ý.`,
        },
      ];

      return await this._makeRequest(messages);
    } catch (error) {
      throw new Error(`Failed to generate market summary: ${error.message}`);
    }
  }

  /**
   * Validates trading signals based on risk management rules
   * @param {Object} signalData - Trading signal data
   * @returns {Promise<Object>} Validation result with recommendations
   */
  async validateTradingSignal(signalData) {
    try {
      const { entry, stopLoss, takeProfit, direction, confidence } = signalData;

      if (!entry || !stopLoss || !takeProfit || !direction) {
        throw new Error("Missing required signal data");
      }

      const riskReward = Math.abs(takeProfit - entry) / Math.abs(entry - stopLoss);
      const riskPercent = Math.abs(entry - stopLoss) / entry * 100;

      const messages = [
        {
          role: "system",
          content: "Bạn là chuyên gia risk management. Đánh giá tín hiệu trading.",
        },
        {
          role: "user",
          content: `Đánh giá tín hiệu:
- Direction: ${direction}
- Entry: ${entry}
- Stop Loss: ${stopLoss}
- Take Profit: ${takeProfit}
- Risk/Reward: 1:${riskReward.toFixed(2)}
- Risk %: ${riskPercent.toFixed(2)}%
- Confidence: ${confidence}%

Đánh giá tín hiệu này có hợp lý không? Đưa ra khuyến nghị.`,
        },
      ];

      const analysis = await this._makeRequest(messages);

      return {
        isValid: riskReward >= 1.5 && riskPercent <= 3 && confidence >= 60,
        riskReward,
        riskPercent,
        analysis,
        recommendations: this._generateRecommendations(riskReward, riskPercent, confidence),
      };
    } catch (error) {
      throw new Error(`Failed to validate trading signal: ${error.message}`);
    }
  }

  /**
   * Generates risk management recommendations
   * @private
   * @param {number} riskReward - Risk/reward ratio
   * @param {number} riskPercent - Risk percentage
   * @param {number} confidence - Confidence level
   * @returns {Array<string>} Array of recommendations
   */
  _generateRecommendations(riskReward, riskPercent, confidence) {
    const recommendations = [];

    if (riskReward < 1.5) {
      recommendations.push("Risk/Reward ratio quá thấp. Nên tìm TP xa hơn hoặc SL gần hơn.");
    }

    if (riskPercent > 3) {
      recommendations.push("Risk % quá cao. Giảm position size hoặc điều chỉnh SL.");
    }

    if (confidence < 60) {
      recommendations.push("Độ tin cậy thấp. Chờ tín hiệu rõ ràng hơn.");
    }

    if (riskReward >= 2 && riskPercent <= 2 && confidence >= 80) {
      recommendations.push("Tín hiệu chất lượng cao. Có thể tăng position size.");
    }

    return recommendations;
  }

  /**
   * Updates the model used for GPT requests
   * @param {string} model - New model name
   */
  setModel(model) {
    this.model = model;
  }

  /**
   * Gets the current model being used
   * @returns {string} Current model name
   */
  getModel() {
    return this.model;
  }
}
