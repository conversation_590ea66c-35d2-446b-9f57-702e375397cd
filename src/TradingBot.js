import { <PERSON><PERSON><PERSON><PERSON> } from "./handlers/ChartHandler.js";
import { <PERSON><PERSON><PERSON><PERSON> } from "./handlers/GPTHandler.js";
import { BinanceHandler } from "./handlers/BinanceHandler.js";
import { Telegraf } from "telegraf";

/**
 * TradingBot - Main orchestrator class that coordinates all trading bot operations
 * Responsibilities:
 * - Initialize and manage handler classes
 * - Coordinate multi-timeframe analysis workflow
 * - Manage bot execution modes (AI Vision, Rule-based, Legacy)
 * - Handle error recovery and fallback mechanisms
 * - Manage Telegram notifications
 * - Provide unified interface for bot operations
 */
export class TradingBot {
  constructor(config = {}) {
    this.config = {
      // Trading configuration
      symbol: config.symbol || process.env.SYMBOL || "ETHUSDT",
      interval: config.interval || process.env.INTERVAL || "1h",

      // Telegram configuration
      telegramToken: config.telegramToken || process.env.TELEGRAM_BOT_TOKEN,
      chatId: config.chatId || process.env.TELEGRAM_GROUP_ID,

      // Execution configuration
      mode: config.mode || "ai_vision", // 'ai_vision', 'rule_based', 'legacy'
      enableFallback: config.enableFallback !== false,
      runInterval: config.runInterval || 60 * 60 * 1000, // 1 hour

      // Handler configurations
      chartConfig: config.chartConfig || {},
      gptConfig: config.gptConfig || {},
      binanceConfig: config.binanceConfig || {},

      ...config,
    };

    // Initialize handlers
    this.initializeHandlers();

    // Initialize Telegram bot
    this.initializeTelegram();

    // Bot state
    this.isRunning = false;
    this.lastRunTime = 0;
    this.runCount = 0;
    this.errors = [];

    console.log(
      `🤖 TradingBot initialized for ${this.config.symbol} in ${this.config.mode} mode`
    );
  }

  /**
   * Initialize all handler classes
   */
  initializeHandlers() {
    try {
      // Chart Handler - manages data and chart generation
      this.chartHandler = new ChartHandler({
        symbol: this.config.symbol,
        ...this.config.chartConfig,
      });

      // GPT Handler - manages AI analysis
      this.gptHandler = new GPTHandler({
        symbol: this.config.symbol,
        ...this.config.gptConfig,
      });

      // Binance Handler - manages market data and trading
      this.binanceHandler = new BinanceHandler({
        defaultSymbol: this.config.symbol,
        ...this.config.binanceConfig,
      });

      console.log("✅ All handlers initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize handlers:", error);
      throw error;
    }
  }

  /**
   * Initialize Telegram bot
   */
  initializeTelegram() {
    if (!this.config.telegramToken || !this.config.chatId) {
      console.warn(
        "⚠️ Telegram configuration missing - notifications disabled"
      );
      this.telegramBot = null;
      return;
    }

    try {
      this.telegramBot = new Telegraf(this.config.telegramToken);
      console.log("✅ Telegram bot initialized");
    } catch (error) {
      console.error("❌ Failed to initialize Telegram bot:", error);
      this.telegramBot = null;
    }
  }

  /**
   * Perform multi-timeframe data analysis
   * @returns {Promise<Object>} Analysis results for all timeframes
   */
  async performMultiTimeframeAnalysis() {
    try {
      console.log("📊 Starting multi-timeframe analysis...");

      // Fetch data for all timeframes
      const allTimeframeData = await this.chartHandler.fetchAllTimeframes();

      // Calculate indicators for each timeframe
      const analysis = {};
      for (const [timeframe, candles] of Object.entries(allTimeframeData)) {
        const indicators = this.chartHandler.calculateIndicators(candles);
        analysis[timeframe] = {
          candles,
          indicators,
          timeframe,
        };
      }

      console.log("✅ Multi-timeframe analysis completed");
      return analysis;
    } catch (error) {
      console.error("❌ Multi-timeframe analysis failed:", error);
      throw error;
    }
  }

  /**
   * Perform AI visual analysis on charts
   * @param {Object} multiTimeframeAnalysis - Analysis data
   * @returns {Promise<Object>} AI analysis results
   */
  async performAIVisualAnalysis(multiTimeframeAnalysis) {
    try {
      console.log("🤖 Starting AI-powered visual chart analysis...");

      // Generate clean charts for AI analysis
      const charts = await this.chartHandler.generateMultiTimeframeChartsForAI(
        multiTimeframeAnalysis
      );

      // Analyze each timeframe with AI vision
      const aiAnalysis = {};
      const analysisPromises = [];

      for (const [timeframe, chartData] of Object.entries(charts)) {
        const currentPrice =
          multiTimeframeAnalysis[timeframe].candles.at(-1).close;

        const promise = this.gptHandler
          .analyzeChartWithAIVision(chartData.buffer, timeframe, currentPrice)
          .then((analysis) => {
            aiAnalysis[timeframe] = analysis;
            console.log(`✅ Successfully analyzed ${timeframe} chart`);
          })
          .catch((error) => {
            console.error(
              `❌ Failed to analyze ${timeframe} chart:`,
              error.message
            );
            aiAnalysis[timeframe] =
              this.gptHandler.createFallbackAnalysis(timeframe);
          });

        analysisPromises.push(promise);
      }

      await Promise.all(analysisPromises);

      console.log("✅ AI visual analysis completed for all timeframes");
      return aiAnalysis;
    } catch (error) {
      console.error("❌ AI visual analysis failed:", error);
      throw error;
    }
  }

  /**
   * Generate AI-powered trading signal
   * @param {Object} aiAnalysis - AI analysis results
   * @param {Object} multiTimeframeAnalysis - Raw analysis data
   * @returns {Promise<Object>} Trading signal
   */
  async generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
    try {
      return await this.gptHandler.generateAITradingSignal(
        aiAnalysis,
        multiTimeframeAnalysis
      );
    } catch (error) {
      console.error("❌ AI trading signal generation failed:", error);
      return this.gptHandler.createFallbackTradingSignal();
    }
  }

  /**
   * Run AI Vision-powered analysis (primary mode)
   * @returns {Promise<Object>} Analysis results
   */
  async runAIVisionAnalysis() {
    console.log("🤖 Starting AI vision-powered trading analysis...");

    // Step 1: Perform multi-timeframe data collection
    const multiTimeframeAnalysis = await this.performMultiTimeframeAnalysis();

    // Step 2: AI Visual Analysis
    const aiAnalysis = await this.performAIVisualAnalysis(
      multiTimeframeAnalysis
    );

    // Step 3: Generate AI-powered trading signal
    const aiTradingSignal = await this.generateAITradingSignal(
      aiAnalysis,
      multiTimeframeAnalysis
    );

    return {
      mode: "ai_vision",
      multiTimeframeAnalysis,
      aiAnalysis,
      aiTradingSignal,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Run rule-based analysis (fallback mode)
   * @returns {Promise<Object>} Analysis results
   */
  async runRuleBasedAnalysis() {
    console.log("🔄 Running rule-based multi-timeframe analysis...");

    // Perform multi-timeframe analysis
    const multiTimeframeAnalysis = await this.performMultiTimeframeAnalysis();

    // TODO: Implement rule-based trading signal generation
    // This would use technical indicators and predefined rules
    const tradingSignal = {
      action: "HOLD",
      confidence: 5,
      reasoning: "Rule-based analysis not yet implemented",
    };

    return {
      mode: "rule_based",
      multiTimeframeAnalysis,
      tradingSignal,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Run legacy single-timeframe analysis (final fallback)
   * @returns {Promise<Object>} Analysis results
   */
  async runLegacyAnalysis() {
    console.log("🔄 Running legacy single-timeframe analysis...");

    // Fetch single timeframe data
    const candles = await this.binanceHandler.getKlines({
      symbol: this.config.symbol,
      interval: this.config.interval,
      limit: 240,
    });

    // Calculate indicators
    const indicators = this.chartHandler.calculateIndicators(candles);

    return {
      mode: "legacy",
      candles,
      indicators,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Send notification via Telegram
   * @param {string} message - Message to send
   * @param {Object} options - Send options
   */
  async sendTelegramNotification(message, options = {}) {
    if (!this.telegramBot || !this.config.chatId) {
      console.log("📱 Telegram not configured - skipping notification");
      return;
    }

    try {
      await this.telegramBot.telegram.sendMessage(this.config.chatId, message, {
        parse_mode: "HTML",
        ...options,
      });
      console.log("📱 Telegram notification sent");
    } catch (error) {
      console.error("❌ Failed to send Telegram notification:", error);
    }
  }

  /**
   * Send chart with analysis via Telegram
   * @param {Buffer} chartBuffer - Chart image buffer
   * @param {string} caption - Chart caption
   */
  async sendTelegramChart(chartBuffer, caption) {
    if (!this.telegramBot || !this.config.chatId) {
      console.log("📱 Telegram not configured - skipping chart");
      return;
    }

    try {
      await this.telegramBot.telegram.sendPhoto(
        this.config.chatId,
        { source: chartBuffer },
        {
          caption,
          parse_mode: "HTML",
        }
      );
      console.log("📱 Telegram chart sent");
    } catch (error) {
      console.error("❌ Failed to send Telegram chart:", error);
    }
  }

  /**
   * Main bot execution method
   * @returns {Promise<Object>} Execution results
   */
  async run() {
    if (this.isRunning) {
      console.log("⚠️ Bot is already running, skipping execution");
      return null;
    }

    this.isRunning = true;
    this.runCount++;
    const startTime = Date.now();

    try {
      console.log(
        `🚀 Starting bot execution #${this.runCount} in ${this.config.mode} mode`
      );

      let results;

      // Execute based on configured mode with fallback chain
      if (this.config.mode === "ai_vision") {
        try {
          results = await this.runAIVisionAnalysis();
        } catch (error) {
          console.error("❌ AI Vision analysis failed:", error);
          if (this.config.enableFallback) {
            console.log("🔄 Falling back to rule-based analysis...");
            results = await this.runRuleBasedAnalysis();
          } else {
            throw error;
          }
        }
      } else if (this.config.mode === "rule_based") {
        try {
          results = await this.runRuleBasedAnalysis();
        } catch (error) {
          console.error("❌ Rule-based analysis failed:", error);
          if (this.config.enableFallback) {
            console.log("🔄 Falling back to legacy analysis...");
            results = await this.runLegacyAnalysis();
          } else {
            throw error;
          }
        }
      } else {
        results = await this.runLegacyAnalysis();
      }

      // Update state
      this.lastRunTime = Date.now();
      const executionTime = this.lastRunTime - startTime;

      console.log(
        `✅ Bot execution #${this.runCount} completed in ${executionTime}ms`
      );

      // Log results summary
      if (results.aiTradingSignal) {
        console.log(
          `📊 AI Recommendation: ${results.aiTradingSignal.trading_recommendation.action}`
        );
        console.log(
          `🎯 AI Confidence: ${results.aiTradingSignal.overall_assessment.final_confidence}/10`
        );
      }

      return {
        ...results,
        executionTime,
        runCount: this.runCount,
      };
    } catch (error) {
      console.error(`❌ Bot execution #${this.runCount} failed:`, error);
      this.errors.push({
        timestamp: new Date().toISOString(),
        error: error.message,
        runCount: this.runCount,
      });

      // Send error notification
      await this.sendTelegramNotification(
        `⚠️ <b>Bot Execution Error</b>\n\nRun #${this.runCount} failed: ${error.message}`,
        { parse_mode: "HTML" }
      );

      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Start automatic bot execution at configured intervals
   */
  startScheduler() {
    if (this.schedulerInterval) {
      console.log("⚠️ Scheduler is already running");
      return;
    }

    console.log(
      `⏰ Starting bot scheduler - running every ${
        this.config.runInterval / 1000 / 60
      } minutes`
    );

    // Run immediately
    this.run().catch((error) => {
      console.error("❌ Initial bot run failed:", error);
    });

    // Schedule recurring runs
    this.schedulerInterval = setInterval(() => {
      this.run().catch((error) => {
        console.error("❌ Scheduled bot run failed:", error);
      });
    }, this.config.runInterval);

    console.log("✅ Bot scheduler started");
  }

  /**
   * Stop automatic bot execution
   */
  stopScheduler() {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
      console.log("⏹️ Bot scheduler stopped");
    } else {
      console.log("⚠️ Scheduler is not running");
    }
  }

  /**
   * Get bot status and statistics
   * @returns {Object} Bot status information
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      mode: this.config.mode,
      symbol: this.config.symbol,
      runCount: this.runCount,
      lastRunTime: this.lastRunTime,
      lastRunAgo: this.lastRunTime ? Date.now() - this.lastRunTime : null,
      schedulerActive: !!this.schedulerInterval,
      runInterval: this.config.runInterval,
      errorCount: this.errors.length,
      recentErrors: this.errors.slice(-5), // Last 5 errors
      cacheStatus: this.chartHandler.getCacheStatus(),
      hasCredentials: this.binanceHandler.hasCredentials(),
      telegramConfigured: !!this.telegramBot,
    };
  }

  /**
   * Test all connections and configurations
   * @returns {Promise<Object>} Test results
   */
  async testConnections() {
    console.log("🔍 Testing all connections...");

    const results = {
      timestamp: new Date().toISOString(),
      binance: null,
      openai: null,
      telegram: null,
      overall: false,
    };

    try {
      // Test Binance connection
      results.binance = await this.binanceHandler.testConnection();
      console.log("✅ Binance connection test completed");
    } catch (error) {
      console.error("❌ Binance connection test failed:", error);
      results.binance = { error: error.message };
    }

    try {
      // Test OpenAI connection (simple test)
      const testAnalysis = this.gptHandler.createFallbackAnalysis("test");
      results.openai = {
        configured: !!this.gptHandler.config.apiKey,
        fallbackWorking: !!testAnalysis,
        models: this.gptHandler.config.models,
      };
      console.log("✅ OpenAI configuration test completed");
    } catch (error) {
      console.error("❌ OpenAI test failed:", error);
      results.openai = { error: error.message };
    }

    try {
      // Test Telegram connection
      if (this.telegramBot) {
        await this.telegramBot.telegram.getMe();
        results.telegram = { configured: true, working: true };
        console.log("✅ Telegram connection test completed");
      } else {
        results.telegram = { configured: false, working: false };
        console.log("⚠️ Telegram not configured");
      }
    } catch (error) {
      console.error("❌ Telegram test failed:", error);
      results.telegram = {
        configured: true,
        working: false,
        error: error.message,
      };
    }

    // Overall status
    results.overall =
      results.binance?.ping &&
      results.openai?.configured &&
      (results.telegram?.working || !this.config.telegramToken);

    console.log(
      `🔍 Connection tests completed - Overall: ${
        results.overall ? "✅" : "❌"
      }`
    );
    return results;
  }

  /**
   * Clear all caches and reset state
   */
  clearCache() {
    this.chartHandler.clearCache();
    this.errors = [];
    console.log("🗑️ All caches cleared and state reset");
  }

  /**
   * Update configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };

    // Reinitialize handlers if their configs changed
    if (newConfig.chartConfig || newConfig.symbol) {
      this.chartHandler = new ChartHandler({
        symbol: this.config.symbol,
        ...this.config.chartConfig,
      });
    }

    if (newConfig.gptConfig || newConfig.symbol) {
      this.gptHandler = new GPTHandler({
        symbol: this.config.symbol,
        ...this.config.gptConfig,
      });
    }

    if (newConfig.binanceConfig || newConfig.symbol) {
      this.binanceHandler = new BinanceHandler({
        defaultSymbol: this.config.symbol,
        ...this.config.binanceConfig,
      });
    }

    console.log("⚙️ Configuration updated");
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log("🛑 Initiating graceful shutdown...");

    // Stop scheduler
    this.stopScheduler();

    // Wait for current run to complete
    while (this.isRunning) {
      console.log("⏳ Waiting for current execution to complete...");
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // Clear caches
    this.clearCache();

    console.log("✅ Graceful shutdown completed");
  }
}
